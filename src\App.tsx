import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout } from './components/Layout';
import { HomePage } from './pages/HomePage';
import { ProductsPage } from './pages/ProductsPage';
import { ProductDetailPage } from './pages/ProductDetailPage';
import { CartPage } from './pages/CartPage';
import { CheckoutPage } from './pages/CheckoutPage';
import { OrderConfirmationPage } from './pages/OrderConfirmationPage';
import { useCart } from './hooks/useCart';
import { ShippingAddress, PaymentMethod } from './types';

function App() {
  const {
    cartItems,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getCartItemsCount
  } = useCart();

  const handleAddToCart = (product: any, quantity: number = 1) => {
    addToCart(product, quantity);
    // You could add a toast notification here
    console.log(`Added ${quantity} ${product.name} to cart`);
  };

  const handlePlaceOrder = (shippingAddress: ShippingAddress, paymentMethod: PaymentMethod) => {
    // In a real app, this would make an API call to process the order
    console.log('Order placed:', {
      items: cartItems,
      shippingAddress,
      paymentMethod,
      total: cartItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0)
    });
    
    // Clear the cart after successful order
    clearCart();
  };

  return (
    <Router>
      <Layout cartItemsCount={getCartItemsCount()}>
        <Routes>
          <Route 
            path="/" 
            element={<HomePage onAddToCart={handleAddToCart} />} 
          />
          <Route 
            path="/products" 
            element={<ProductsPage onAddToCart={handleAddToCart} />} 
          />
          <Route 
            path="/product/:id" 
            element={<ProductDetailPage onAddToCart={handleAddToCart} />} 
          />
          <Route 
            path="/cart" 
            element={
              <CartPage 
                cartItems={cartItems}
                onUpdateQuantity={updateQuantity}
                onRemoveFromCart={removeFromCart}
                onClearCart={clearCart}
              />
            } 
          />
          <Route 
            path="/checkout" 
            element={
              <CheckoutPage 
                cartItems={cartItems}
                onPlaceOrder={handlePlaceOrder}
              />
            } 
          />
          <Route 
            path="/order-confirmation" 
            element={<OrderConfirmationPage />} 
          />
          {/* Category routes */}
          <Route 
            path="/category/:category" 
            element={<ProductsPage onAddToCart={handleAddToCart} />} 
          />
          {/* Fallback route */}
          <Route 
            path="*" 
            element={
              <div className="container py-16 text-center">
                <h1 className="text-2xl font-bold mb-4">Page Not Found</h1>
                <p className="text-gray-600 mb-8">The page you're looking for doesn't exist.</p>
                <a href="/" className="btn btn-primary">Go Home</a>
              </div>
            } 
          />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;
