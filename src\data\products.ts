import { Product, Review, Offer } from '../types';

export const products: Product[] = [
  {
    id: '1',
    name: 'iPhone 15 Pro',
    description: 'The most advanced iPhone yet with titanium design, A17 Pro chip, and professional camera system.',
    price: 999,
    originalPrice: 1099,
    discount: 9,
    category: 'Electronics',
    brand: 'Apple',
    images: [
      'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500',
      'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500'
    ],
    rating: 4.8,
    reviewCount: 1247,
    inStock: true,
    stockQuantity: 25,
    features: ['A17 Pro chip', 'Titanium design', 'Pro camera system', '5G connectivity'],
    specifications: {
      'Display': '6.1-inch Super Retina XDR',
      'Storage': '128GB',
      'Camera': '48MP Main + 12MP Ultra Wide',
      'Battery': 'Up to 23 hours video playback'
    }
  },
  {
    id: '2',
    name: 'MacBook Air M2',
    description: 'Supercharged by M2 chip. Incredibly thin and light laptop with all-day battery life.',
    price: 1199,
    originalPrice: 1299,
    discount: 8,
    category: 'Electronics',
    brand: 'Apple',
    images: [
      'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=500',
      'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=500'
    ],
    rating: 4.7,
    reviewCount: 892,
    inStock: true,
    stockQuantity: 15,
    features: ['M2 chip', '13.6-inch display', 'All-day battery', 'Fanless design'],
    specifications: {
      'Processor': 'Apple M2 chip',
      'Memory': '8GB unified memory',
      'Storage': '256GB SSD',
      'Display': '13.6-inch Liquid Retina'
    }
  },
  {
    id: '3',
    name: 'Sony WH-1000XM5',
    description: 'Industry-leading noise canceling headphones with exceptional sound quality.',
    price: 349,
    originalPrice: 399,
    discount: 13,
    category: 'Audio',
    brand: 'Sony',
    images: [
      'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500',
      'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=500'
    ],
    rating: 4.6,
    reviewCount: 2156,
    inStock: true,
    stockQuantity: 42,
    features: ['Industry-leading noise canceling', '30-hour battery', 'Quick charge', 'Multipoint connection'],
    specifications: {
      'Driver': '30mm',
      'Battery Life': '30 hours',
      'Charging': 'USB-C quick charge',
      'Weight': '250g'
    }
  },
  {
    id: '4',
    name: 'Nike Air Max 270',
    description: 'Lifestyle shoe with the largest Max Air unit yet for all-day comfort.',
    price: 150,
    originalPrice: 180,
    discount: 17,
    category: 'Fashion',
    brand: 'Nike',
    images: [
      'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500',
      'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=500'
    ],
    rating: 4.4,
    reviewCount: 3421,
    inStock: true,
    stockQuantity: 78,
    features: ['Max Air cushioning', 'Breathable mesh', 'Durable rubber outsole', 'Lifestyle design'],
    specifications: {
      'Upper': 'Mesh and synthetic',
      'Midsole': 'Max Air unit',
      'Outsole': 'Rubber',
      'Fit': 'True to size'
    }
  },
  {
    id: '5',
    name: 'Samsung 55" QLED TV',
    description: '4K QLED Smart TV with Quantum Dot technology and HDR support.',
    price: 799,
    originalPrice: 999,
    discount: 20,
    category: 'Electronics',
    brand: 'Samsung',
    images: [
      'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=500',
      'https://images.unsplash.com/photo-1461151304267-38535e780c79?w=500'
    ],
    rating: 4.5,
    reviewCount: 1876,
    inStock: true,
    stockQuantity: 12,
    features: ['4K QLED display', 'Quantum Dot technology', 'Smart TV platform', 'HDR10+ support'],
    specifications: {
      'Screen Size': '55 inches',
      'Resolution': '4K UHD (3840 x 2160)',
      'HDR': 'HDR10+',
      'Smart Platform': 'Tizen OS'
    }
  },
  {
    id: '6',
    name: 'Adidas Ultraboost 22',
    description: 'Running shoes with responsive Boost midsole and Primeknit upper.',
    price: 190,
    category: 'Fashion',
    brand: 'Adidas',
    images: [
      'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=500',
      'https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?w=500'
    ],
    rating: 4.3,
    reviewCount: 2847,
    inStock: true,
    stockQuantity: 56,
    features: ['Boost midsole', 'Primeknit upper', 'Continental rubber outsole', 'Energy return'],
    specifications: {
      'Upper': 'Primeknit',
      'Midsole': 'Boost foam',
      'Outsole': 'Continental rubber',
      'Drop': '10mm'
    }
  }
];

export const reviews: Review[] = [
  {
    id: '1',
    productId: '1',
    userId: 'user1',
    userName: 'John Smith',
    rating: 5,
    title: 'Amazing phone!',
    comment: 'The camera quality is incredible and the titanium build feels premium. Battery life is excellent.',
    date: '2024-01-15',
    helpful: 24,
    verified: true
  },
  {
    id: '2',
    productId: '1',
    userId: 'user2',
    userName: 'Sarah Johnson',
    rating: 4,
    title: 'Great upgrade',
    comment: 'Coming from iPhone 13, this is a solid upgrade. The Action Button is very useful.',
    date: '2024-01-10',
    helpful: 18,
    verified: true
  },
  {
    id: '3',
    productId: '3',
    userId: 'user3',
    userName: 'Mike Chen',
    rating: 5,
    title: 'Best noise canceling headphones',
    comment: 'The noise canceling is phenomenal. Perfect for flights and commuting.',
    date: '2024-01-12',
    helpful: 31,
    verified: true
  }
];

export const offers: Offer[] = [
  {
    id: '1',
    title: 'New Year Sale',
    description: 'Up to 20% off on electronics',
    discountPercentage: 20,
    validUntil: '2024-02-01',
    productIds: ['1', '2', '5'],
    type: 'percentage'
  },
  {
    id: '2',
    title: 'Fashion Week Special',
    description: '15% off on all footwear',
    discountPercentage: 15,
    validUntil: '2024-01-31',
    productIds: ['4', '6'],
    type: 'percentage'
  }
];
