import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Search, ShoppingCart, User, Menu, X } from 'lucide-react';

interface HeaderProps {
  cartItemsCount: number;
}

export const Header: React.FC<HeaderProps> = ({ cartItemsCount }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const categories = ['Electronics', 'Fashion', 'Audio', 'Home', 'Sports'];

  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      <div className="container">
        {/* Top bar */}
        <div className="flex items-center justify-between py-4">
          {/* Logo */}
          <Link to="/" className="text-2xl font-bold text-blue-600">
            ShopHub
          </Link>

          {/* Search bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-2xl mx-8">
            <div className="relative w-full">
              <input
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <Search className="absolute left-3 top-2.5 w-5 h-5 text-gray-400" />
            </div>
          </div>

          {/* Right side icons */}
          <div className="flex items-center gap-4">
            {/* User account */}
            <Link to="/account" className="hidden md:flex items-center gap-2 hover:text-blue-600">
              <User className="w-5 h-5" />
              <span>Account</span>
            </Link>

            {/* Cart */}
            <Link to="/cart" className="relative hover:text-blue-600">
              <ShoppingCart className="w-6 h-6" />
              {cartItemsCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {cartItemsCount}
                </span>
              )}
            </Link>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Navigation - Desktop */}
        <nav className="hidden md:block border-t border-gray-200">
          <div className="flex items-center gap-8 py-3">
            <Link to="/" className="hover:text-blue-600 font-medium">
              Home
            </Link>
            {categories.map((category) => (
              <Link
                key={category}
                to={`/category/${category.toLowerCase()}`}
                className="hover:text-blue-600"
              >
                {category}
              </Link>
            ))}
            <Link to="/offers" className="hover:text-blue-600 text-red-600 font-medium">
              Special Offers
            </Link>
          </div>
        </nav>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-200">
            {/* Mobile search */}
            <div className="p-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <Search className="absolute left-3 top-2.5 w-5 h-5 text-gray-400" />
              </div>
            </div>

            {/* Mobile navigation */}
            <nav className="px-4 pb-4">
              <div className="flex flex-col gap-3">
                <Link
                  to="/"
                  className="py-2 hover:text-blue-600 font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Home
                </Link>
                {categories.map((category) => (
                  <Link
                    key={category}
                    to={`/category/${category.toLowerCase()}`}
                    className="py-2 hover:text-blue-600"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {category}
                  </Link>
                ))}
                <Link
                  to="/offers"
                  className="py-2 hover:text-blue-600 text-red-600 font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Special Offers
                </Link>
                <Link
                  to="/account"
                  className="py-2 hover:text-blue-600 border-t border-gray-200 mt-2 pt-4"
                  onClick={() => setIsMenuOpen(false)}
                >
                  My Account
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};
